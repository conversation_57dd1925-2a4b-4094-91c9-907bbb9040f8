import 'dart:async';
import 'dart:developer' as developer;
import 'dart:isolate';

import 'package:budapp/services/logging_service.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

/// Comprehensive global error handler with Firebase Crashlytics integration
///
/// Provides centralized error handling for:
/// - Flutter framework errors (FlutterError.onError)
/// - Platform dispatcher errors (PlatformDispatcher.instance.onError)
/// - Isolate errors (Isolate.current.addErrorListener)
/// - Custom error reporting with user context
/// - Environment-aware crash reporting configuration
class GlobalErrorHandler {
  factory GlobalErrorHandler() => _instance ??= GlobalErrorHandler._();

  GlobalErrorHandler._();

  static GlobalErrorHandler? _instance;
  bool _isInitialized = false;
  late LoggingService _logger;

  /// Initialize global error handling with Crashlytics integration
  Future<void> initialize({
    required LoggingService logger,
    bool enableInDebug = false,
  }) async {
    if (_isInitialized) return;

    _logger = logger;

    try {
      // Configure Crashlytics collection based on environment
      await _configureCrashlyticsCollection(enableInDebug);

      // Set up global error handlers
      _setupFlutterErrorHandler();
      _setupPlatformDispatcherErrorHandler();
      _setupIsolateErrorHandler();

      _isInitialized = true;
      _logger.info('Global error handler initialized successfully');
    } catch (error, stackTrace) {
      _logger.error(
        'Failed to initialize global error handler',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Configure Crashlytics collection based on build mode and environment
  Future<void> _configureCrashlyticsCollection(bool enableInDebug) async {
    final shouldEnable = kReleaseMode || enableInDebug;

    try {
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(
        shouldEnable,
      );

      if (shouldEnable) {
        _logger.info('Crashlytics collection enabled');
      } else {
        _logger.debug('Crashlytics collection disabled for debug build');
      }
    } on Exception catch (error) {
      // Handle Firebase not initialized (common in test environments)
      _logger.debug(
        'Crashlytics configuration failed (Firebase not initialized): $error',
      );
      // Don't rethrow - this is expected in test environments
    }
  }

  /// Set up Flutter framework error handler
  void _setupFlutterErrorHandler() {
    FlutterError.onError = (FlutterErrorDetails errorDetails) {
      // Log to console in debug mode
      if (kDebugMode) {
        FlutterError.presentError(errorDetails);
      }

      // Log through logging service
      _logger.error(
        'Flutter framework error: ${errorDetails.exceptionAsString()}',
        error: errorDetails.exception,
        stackTrace: errorDetails.stack,
      );

      // Report to Crashlytics as fatal error (if Firebase is initialized)
      try {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      } on Exception catch (error) {
        _logger.debug('Failed to report Flutter error to Crashlytics: $error');
      }
    };
  }

  /// Set up platform dispatcher error handler for asynchronous errors
  void _setupPlatformDispatcherErrorHandler() {
    PlatformDispatcher.instance.onError = (error, stackTrace) {
      // Log through logging service
      _logger.error(
        'Platform dispatcher error: $error',
        error: error,
        stackTrace: stackTrace,
      );

      // Report to Crashlytics as fatal error (if Firebase is initialized)
      try {
        FirebaseCrashlytics.instance.recordError(
          error,
          stackTrace,
          fatal: true,
          information: ['Caught by PlatformDispatcher.onError'],
        );
      } on Exception catch (crashlyticsError) {
        _logger.debug(
          'Failed to report platform error to Crashlytics: $crashlyticsError',
        );
      }

      return true; // Indicates error was handled
    };
  }

  /// Set up isolate error handler for errors outside Flutter context
  void _setupIsolateErrorHandler() {
    Isolate.current.addErrorListener(
      RawReceivePort((dynamic pair) async {
        final errorAndStackTrace = pair as List<dynamic>;
        final error = errorAndStackTrace.first;
        final stackTrace = errorAndStackTrace.last as StackTrace?;

        // Log through logging service
        _logger.error(
          'Isolate error: $error',
          error: error,
          stackTrace: stackTrace,
        );

        // Report to Crashlytics as fatal error (if Firebase is initialized)
        try {
          await FirebaseCrashlytics.instance.recordError(
            error,
            stackTrace,
            fatal: true,
            information: ['Caught by Isolate error listener'],
          );
        } on Exception catch (crashlyticsError) {
          _logger.debug(
            'Failed to report isolate error to Crashlytics: $crashlyticsError',
          );
        }
      }).sendPort,
    );
  }

  /// Report non-fatal error with context and user information
  Future<void> reportError(
    Object error,
    StackTrace? stackTrace, {
    String? reason,
    Map<String, dynamic>? customKeys,
    List<String>? breadcrumbs,
    bool fatal = false,
  }) async {
    if (!_isInitialized) {
      developer.log(
        'GlobalErrorHandler not initialized, falling back to basic logging: $error',
      );
      return;
    }

    try {
      // Log through logging service
      _logger.error(
        reason ?? 'Application error: $error',
        error: error,
        stackTrace: stackTrace,
      );

      // Set custom keys for crash report context
      if (customKeys != null) {
        for (final entry in customKeys.entries) {
          await _setCustomKey(entry.key, entry.value);
        }
      }

      // Add breadcrumb logs (if Firebase is initialized)
      if (breadcrumbs != null) {
        for (final breadcrumb in breadcrumbs) {
          try {
            unawaited(FirebaseCrashlytics.instance.log(breadcrumb));
          } on Exception catch (crashlyticsError) {
            _logger.debug(
              'Failed to log breadcrumb to Crashlytics: $crashlyticsError',
            );
          }
        }
      }

      // Report to Crashlytics (if Firebase is initialized)
      try {
        await FirebaseCrashlytics.instance.recordError(
          error,
          stackTrace,
          fatal: fatal,
          reason: reason,
          information: breadcrumbs ?? [],
        );
      } on Exception catch (crashlyticsError) {
        _logger.debug(
          'Failed to record error to Crashlytics: $crashlyticsError',
        );
      }
    } on Exception catch (reportingError, reportingStackTrace) {
      // Fallback logging if crash reporting fails
      _logger.error(
        'Failed to report error to crash reporting service',
        error: reportingError,
        stackTrace: reportingStackTrace,
      );
    }
  }

  /// Set custom key with type safety and size limits
  Future<void> _setCustomKey(String key, dynamic value) async {
    try {
      // Enforce key/value size limits (Crashlytics limits to 1kB per pair)
      final keyStr = key.length > 100 ? key.substring(0, 100) : key;

      if (value is String) {
        final valueStr = value.length > 900
            ? '${value.substring(0, 897)}...'
            : value;
        try {
          unawaited(
            FirebaseCrashlytics.instance.setCustomKey(keyStr, valueStr),
          );
        } on Exception catch (crashlyticsError) {
          _logger.debug('Failed to set string custom key: $crashlyticsError');
        }
      } else if (value is bool) {
        try {
          unawaited(FirebaseCrashlytics.instance.setCustomKey(keyStr, value));
        } on Exception catch (crashlyticsError) {
          _logger.debug('Failed to set bool custom key: $crashlyticsError');
        }
      } else if (value is int) {
        try {
          unawaited(FirebaseCrashlytics.instance.setCustomKey(keyStr, value));
        } on Exception catch (crashlyticsError) {
          _logger.debug('Failed to set int custom key: $crashlyticsError');
        }
      } else if (value is double) {
        try {
          unawaited(FirebaseCrashlytics.instance.setCustomKey(keyStr, value));
        } on Exception catch (crashlyticsError) {
          _logger.debug('Failed to set double custom key: $crashlyticsError');
        }
      } else {
        // Convert complex types to string with size limit
        final valueStr = value.toString();
        final truncatedStr = valueStr.length > 900
            ? '${valueStr.substring(0, 897)}...'
            : valueStr;
        try {
          unawaited(
            FirebaseCrashlytics.instance.setCustomKey(keyStr, truncatedStr),
          );
        } on Exception catch (crashlyticsError) {
          _logger.debug('Failed to set complex custom key: $crashlyticsError');
        }
      }
    } on Exception catch (error) {
      _logger.debug('Failed to set custom key $key: $error');
    }
  }

  /// Set user identifier for crash reports (with PII protection)
  Future<void> setUserIdentifier(String? userId) async {
    if (!_isInitialized) return;

    try {
      // Clear identifier if null provided
      final sanitizedId = (userId?.isEmpty ?? true) ? '' : userId;
      try {
        await FirebaseCrashlytics.instance.setUserIdentifier(sanitizedId ?? '');
        _logger.debug('User identifier set for crash reporting');
      } on Exception catch (crashlyticsError) {
        _logger.debug(
          'Failed to set user identifier in Crashlytics: $crashlyticsError',
        );
      }
    } on Exception catch (error) {
      _logger.debug('Failed to set user identifier: $error');
    }
  }

  /// Add breadcrumb log for crash context
  void logBreadcrumb(String message) {
    if (!_isInitialized) return;

    try {
      // Limit message size (Crashlytics has 64kB total log limit)
      final truncatedMessage = message.length > 500
          ? '${message.substring(0, 497)}...'
          : message;
      try {
        unawaited(FirebaseCrashlytics.instance.log(truncatedMessage));
        _logger.debug('Breadcrumb logged: $truncatedMessage');
      } on Exception catch (crashlyticsError) {
        _logger.debug(
          'Failed to log breadcrumb to Crashlytics: $crashlyticsError',
        );
      }
    } on Exception catch (error) {
      _logger.debug('Failed to log breadcrumb: $error');
    }
  }

  /// Test crash reporting setup (use only for debugging)
  Future<void> testCrash() async {
    if (!kDebugMode) {
      _logger.warning('Test crash attempted in non-debug mode - ignoring');
      return;
    }

    _logger.warning('Triggering test crash for Crashlytics verification');
    await Future<void>.delayed(const Duration(seconds: 1));
    try {
      FirebaseCrashlytics.instance.crash();
    } on Exception catch (error) {
      _logger.debug('Failed to trigger test crash: $error');
    }
  }

  /// Get current error handler initialization status
  bool get isInitialized => _isInitialized;

  /// Reset the error handler for testing purposes
  /// This should only be used in test environments
  @visibleForTesting
  void resetForTesting() {
    _isInitialized = false;
    // Note: We don't reset _logger as it's a late field
    // Tests should call initialize() after reset if needed
  }
}
