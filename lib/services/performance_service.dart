import 'package:budapp/services/logging_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_performance/firebase_performance.dart';

/// Service for managing Firebase Performance Monitoring with 2025 best practices
///
/// Provides comprehensive performance tracking for:
/// - App startup times and critical user journeys
/// - Firestore operations and network requests
/// - Screen transitions and UI responsiveness
/// - Custom business logic performance
/// - Memory usage and resource optimization
// ignore: avoid_classes_with_only_static_members
class PerformanceService {
  static FirebasePerformance? _performance;
  static final Map<String, Trace> _activeTraces = {};
  static final Map<String, HttpMetric> _activeHttpMetrics = {};
  static final _log = LoggingService();
  static bool _isInitialized = false;

  // Performance thresholds (2025 standards)
  static const Duration _appStartupThreshold = Duration(seconds: 2);
  static const Duration _screenTransitionThreshold = Duration(
    milliseconds: 100,
  );
  static const Duration _firestoreQueryThreshold = Duration(seconds: 1);

  /// Check if Firebase is initialized
  static bool get _isFirebaseInitialized {
    try {
      return Firebase.apps.isNotEmpty;
    } on Exception {
      return false;
    }
  }

  /// Initialize Performance Monitoring with 2025 standards
  static Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    try {
      // Check if Firebase is initialized before accessing performance monitoring
      if (!_isFirebaseInitialized) {
        _log.info(
          'Firebase not initialized, skipping performance monitoring setup',
        );
        _isInitialized = true;
        return;
      }

      // Performance monitoring is automatically initialized with Firebase
      _performance = FirebasePerformance.instance;

      // Enable collection in all modes for development visibility
      // In production, this should be controlled by build configuration
      await _performance!.setPerformanceCollectionEnabled(true);
      _log.info('Performance monitoring initialized (collection enabled)');

      // Start app startup trace immediately
      await startTrace('app_startup');
      await setTraceAttribute('app_startup', 'startup_type', 'cold');

      _log.info('Performance monitoring configured with 2025 standards');
    } on Exception catch (e) {
      _log.error('Failed to initialize performance monitoring', error: e);
    } finally {
      _isInitialized = true;
    }
  }

  /// Start a custom trace
  static Future<void> startTrace(String traceName) async {
    try {
      if (_activeTraces.containsKey(traceName)) {
        _log.debug('Trace $traceName is already active');
        return;
      }

      // Skip if performance monitoring is not available
      if (_performance == null) {
        _log.debug(
          'Performance monitoring not available, skipping trace: $traceName',
        );
        return;
      }

      final trace = _performance!.newTrace(traceName);
      await trace.start();
      _activeTraces[traceName] = trace;
      _log.debug('Started trace: $traceName');
    } on Exception {
      // _log.debug('Failed to start trace $traceName: $e');
    }
  }

  /// Stop a custom trace
  static Future<void> stopTrace(String traceName) async {
    try {
      final trace = _activeTraces.remove(traceName);
      if (trace != null) {
        await trace.stop();
        _log.debug('Stopped trace: $traceName (data sent to Firebase)');
      } else {
        // _log.debug('Trace $traceName not found');
      }
    } on Exception catch (e) {
      _log.error('Failed to stop trace $traceName', error: e);
    }
  }

  /// Add a metric to an active trace
  static Future<void> setTraceMetric(
    String traceName,
    String metricName,
    int value,
  ) async {
    try {
      final trace = _activeTraces[traceName];
      if (trace != null) {
        trace.setMetric(metricName, value);
        _log.debug('Set metric $metricName=$value for trace $traceName');
      } else {
        _log.debug('Trace $traceName not found for metric $metricName');
      }
    } on Exception catch (e) {
      _log.error(
        'Failed to set metric $metricName for trace $traceName',
        error: e,
      );
    }
  }

  /// Increment a metric in an active trace
  static Future<void> incrementTraceMetric(
    String traceName,
    String metricName, [
    int incrementBy = 1,
  ]) async {
    try {
      final trace = _activeTraces[traceName];
      if (trace != null) {
        trace.incrementMetric(metricName, incrementBy);
        _log.debug(
          'Incremented metric $metricName by $incrementBy for trace $traceName',
        );
      } else {
        _log.debug('Trace $traceName not found for metric $metricName');
      }
    } on Exception catch (e) {
      _log.error(
        'Failed to increment metric $metricName for trace $traceName',
        error: e,
      );
    }
  }

  /// Add an attribute to an active trace
  static Future<void> setTraceAttribute(
    String traceName,
    String attributeName,
    String value,
  ) async {
    try {
      final trace = _activeTraces[traceName];
      if (trace != null) {
        trace.putAttribute(attributeName, value);
        _log.debug('Set attribute $attributeName=$value for trace $traceName');
      } else {
        _log.debug('Trace $traceName not found for attribute $attributeName');
      }
    } on Exception catch (e) {
      _log.error(
        'Failed to set attribute $attributeName for trace $traceName',
        error: e,
      );
    }
  }

  /// Create and track an HTTP request metric
  static HttpMetric? newHttpMetric(String url, HttpMethod httpMethod) {
    if (_performance == null) {
      _log.debug('Performance monitoring not available, skipping HTTP metric');
      return null;
    }
    return _performance!.newHttpMetric(url, httpMethod);
  }

  /// Common trace names for the app
  static const String appStartupTrace = 'app_startup';
  static const String authInitTrace = 'auth_initialization';
  static const String firestoreInitTrace = 'firestore_initialization';
  static const String remoteConfigTrace = 'remote_config_fetch';
  static const String userLoginTrace = 'user_login';
  static const String dataLoadTrace = 'data_load';
  static const String screenLoadTrace = 'screen_load';

  /// Convenience method to track app startup performance
  static Future<void> trackAppStartup() async {
    await startTrace(appStartupTrace);
  }

  /// Convenience method to complete app startup tracking
  static Future<void> completeAppStartup() async {
    await stopTrace(appStartupTrace);
  }

  /// Track screen navigation performance
  static Future<void> trackScreenLoad(String screenName) async {
    final traceName = '${screenLoadTrace}_$screenName';
    await startTrace(traceName);
    await setTraceAttribute(traceName, 'screen_name', screenName);
  }

  /// Complete screen load tracking
  static Future<void> completeScreenLoad(String screenName) async {
    final traceName = '${screenLoadTrace}_$screenName';
    await stopTrace(traceName);
  }

  /// Track authentication flow performance
  static Future<void> trackAuthFlow(String flowType) async {
    final traceName = '${userLoginTrace}_$flowType';
    await startTrace(traceName);
    await setTraceAttribute(traceName, 'auth_flow', flowType);
  }

  /// Complete authentication flow tracking
  static Future<void> completeAuthFlow(String flowType) async {
    final traceName = '${userLoginTrace}_$flowType';
    await stopTrace(traceName);
  }

  // 2025 Enhanced Performance Monitoring Methods

  /// Track Firestore query performance with automatic threshold monitoring
  static Future<void> trackFirestoreQuery(
    String queryName,
    Future<void> Function() queryFunction,
  ) async {
    // Skip performance tracking if Firebase is not initialized
    if (_performance == null) {
      _log.debug(
        'Performance monitoring not available, executing query without tracking: $queryName',
      );
      await queryFunction();
      return;
    }

    final traceName = 'firestore_query_$queryName';
    final stopwatch = Stopwatch()..start();

    await startTrace(traceName);
    await setTraceAttribute(traceName, 'query_type', queryName);

    try {
      await queryFunction();
      stopwatch.stop();

      // Check if query exceeded threshold
      if (stopwatch.elapsed > _firestoreQueryThreshold) {
        await setTraceAttribute(traceName, 'slow_query', 'true');
        _log.warning(
          'Slow Firestore query detected: $queryName took ${stopwatch.elapsed.inMilliseconds}ms',
        );
      }

      await setTraceMetric(
        traceName,
        'duration_ms',
        stopwatch.elapsed.inMilliseconds,
      );
    } finally {
      await stopTrace(traceName);
    }
  }

  /// Track HTTP request performance with automatic metrics
  static Future<T> trackHttpRequest<T>(
    String url,
    HttpMethod method,
    Future<T> Function() requestFunction,
  ) async {
    // Skip if performance monitoring is not available
    if (_performance == null) {
      _log.debug(
        'Performance monitoring not available, skipping HTTP tracking',
      );
      return requestFunction();
    }

    final metric = _performance!.newHttpMetric(url, method);
    final requestId = '${method.name}_${url.hashCode}';

    _activeHttpMetrics[requestId] = metric;

    try {
      await metric.start();
      final result = await requestFunction();

      // Set response code if successful
      metric.httpResponseCode = 200;
      metric.responsePayloadSize =
          0; // Would need actual size in real implementation

      return result;
    } catch (e) {
      // Set error response code
      metric.httpResponseCode = 500;
      rethrow;
    } finally {
      await metric.stop();
      _activeHttpMetrics.remove(requestId);
    }
  }

  /// Track screen transition performance with threshold monitoring
  static Future<void> trackScreenTransition(
    String fromScreen,
    String toScreen,
    Future<void> Function() transitionFunction,
  ) async {
    final traceName = 'screen_transition_${fromScreen}_to_$toScreen';
    final stopwatch = Stopwatch()..start();

    await startTrace(traceName);
    await setTraceAttribute(traceName, 'from_screen', fromScreen);
    await setTraceAttribute(traceName, 'to_screen', toScreen);

    try {
      await transitionFunction();
      stopwatch.stop();

      // Check if transition exceeded threshold
      if (stopwatch.elapsed > _screenTransitionThreshold) {
        await setTraceAttribute(traceName, 'slow_transition', 'true');
        _log.warning(
          'Slow screen transition detected: $fromScreen -> $toScreen took ${stopwatch.elapsed.inMilliseconds}ms',
        );
      }

      await setTraceMetric(
        traceName,
        'duration_ms',
        stopwatch.elapsed.inMilliseconds,
      );
    } finally {
      await stopTrace(traceName);
    }
  }

  /// Complete app startup with threshold monitoring
  static Future<void> completeAppStartupWithMetrics() async {
    final trace = _activeTraces['app_startup'];
    if (trace != null) {
      // Calculate startup time (approximate)
      final startupTime = DateTime.now().millisecondsSinceEpoch;

      if (Duration(milliseconds: startupTime) > _appStartupThreshold) {
        await setTraceAttribute('app_startup', 'slow_startup', 'true');
        _log.warning(
          'Slow app startup detected: exceeded ${_appStartupThreshold.inSeconds}s threshold',
        );
      }

      await setTraceMetric('app_startup', 'startup_time_ms', startupTime);
      await stopTrace('app_startup');
    }
  }

  /// Track memory usage and performance
  static Future<void> trackMemoryUsage(String context) async {
    final traceName = 'memory_usage_$context';
    await startTrace(traceName);
    await setTraceAttribute(traceName, 'context', context);

    // Note: Actual memory tracking would require platform-specific implementation
    // This is a placeholder for the framework
    await setTraceMetric(traceName, 'memory_mb', 0);
    await stopTrace(traceName);
  }

  /// Get performance thresholds for external use
  static Duration get appStartupThreshold => _appStartupThreshold;
  static Duration get screenTransitionThreshold => _screenTransitionThreshold;
  static Duration get firestoreQueryThreshold => _firestoreQueryThreshold;
}
